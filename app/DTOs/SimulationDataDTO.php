<?php

namespace App\DTOs;

use Illuminate\Support\Collection;

class SimulationDataDTO
{
    public function __construct(
        public array $standings,
        public int $currentWeek,
        public array $predictions,
        public bool $seasonComplete,
        public Collection $matches,
        public string $seasonId
    ) {}

    public function toArray(): array
    {
        return [
            'standings' => $this->standings,
            'current_week' => $this->currentWeek,
            'predictions' => $this->predictions,
            'season_complete' => $this->seasonComplete,
            'matches' => $this->matches,
            'seasonId' => $this->seasonId
        ];
    }

    public function toControllerArray(): array
    {
        return [
            'standings' => $this->standings,
            'current_week' => $this->currentWeek,
            'predictions' => $this->predictions,
            'season_complete' => $this->seasonComplete,
            'seasonId' => $this->seasonId
        ];
    }
}
