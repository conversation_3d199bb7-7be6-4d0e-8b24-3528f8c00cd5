<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Http\Resources\SimulationResource;
use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use App\Services\SimulationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class LeagueController extends Controller
{
    protected SimulationService $simulationService;

    public function __construct(SimulationService $simulationService)
    {
        $this->simulationService = $simulationService;
    }
    public function getTeams()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }


    public function startSimulation(FixtureGenerator $generator): RedirectResponse
    {
        $teamIds = Team::pluck('id')->toArray();

        $seasonId = Str::random(8);
        $generator->generate($teamIds, $seasonId);

        return redirect()->route('simulation')
            ->cookie('simulation_season_id', $seasonId, 60 * 24 * 7);
    }


    public function simulationIndex(Request $request): Response|RedirectResponse
    {
        $cookieSeasonId = $request->cookie('simulation_season_id');
        $season = LeagueMatches::where('season_id', $cookieSeasonId)->exists();

        if ($season) {
            $seasonId = $cookieSeasonId;
        } else {
            return redirect()->route('/');
        }

        $simulationData = $this->getSimulationData($seasonId);

        return Inertia::render('Simulation', $simulationData);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        $simulationData = $this->getSimulationData($seasonId);

        return response()->json([
            'played_count' => $updateCount,
            'fixtures' => $simulationData['fixtures'],
            'standings' => $simulationData['standings'],
            'predictions' => $simulationData['predictions'],
            'current_week' => $simulationData['current_week'],
            'season_complete' => $simulationData['season_complete'],
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $unplayedMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        $updateCount = 0;
        foreach ($unplayedMatches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        $simulationData = $this->getSimulationData($seasonId);

        return response()->json([
            'message' => "Week {$nextWeek} matches played.",
            'played_count' => $updateCount,
            'week' => $nextWeek,
            'fixtures' => $simulationData['fixtures'],
            'standings' => $simulationData['standings'],
            'predictions' => $simulationData['predictions'],
            'current_week' => $simulationData['current_week'],
            'season_complete' => $simulationData['season_complete'],
        ]);
    }


    /**
     * @param mixed $match
     * @return bool
     */
    public function simulateMatchAsPlayed(mixed $match): bool
    {
        return $match->update([
            'home_score' => rand(0, 5),
            'away_score' => rand(0, 5),
            'is_played' => true,
            'played_at' => now(),
        ]);
    }


    public function getUpdatedFixtures(string $seasonId): array
    {
        $updatedFixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->orderBy('week')
            ->orderBy('id')
            ->get();

        return LeagueMatchResource::groupByWeek($updatedFixtures);
    }

    private function getSimulationData(string $seasonId): array
    {
        // Use optimized service method that fetches all data in a single query
        $simulationData = $this->simulationService->getCompleteSimulationData($seasonId);

        // Transform matches to fixtures using the existing resource
        $fixtures = LeagueMatchResource::collection($simulationData['matches'])->resolve();

        return [
            'fixtures' => $fixtures,
            'standings' => $simulationData['standings'],
            'predictions' => $simulationData['predictions'],
            'current_week' => $simulationData['current_week'],
            'season_complete' => $simulationData['season_complete'],
            'seasonId' => $seasonId
        ];
    }
}
