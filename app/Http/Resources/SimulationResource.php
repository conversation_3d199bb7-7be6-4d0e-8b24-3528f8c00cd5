<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimulationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'fixtures' => LeagueMatchResource::collection($this->resource['fixtures'])->resolve(),
            'standings' => $this->resource['standings'],
            'predictions' => $this->resource['predictions'],
            'current_week' => $this->resource['current_week'],
            'season_complete' => $this->resource['season_complete'],
            'season_id' => $this->resource['season_id'],
        ];
    }
}
