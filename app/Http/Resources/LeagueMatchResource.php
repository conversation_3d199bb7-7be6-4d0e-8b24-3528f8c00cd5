<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class LeagueMatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'home_team' => [
                "id" => $this->homeTeam->id,
                "name" => $this->homeTeam->name,
            ],
            'away_team' => [
                "id" => $this->awayTeam->id,
                "name" => $this->awayTeam->name,
            ],
            'week' => $this->week,
            'home_score' => $this->home_score,
            'away_score' => $this->away_score,
            'is_played' => $this->is_played,
        ];
    }

    /**
     * Group matches by week
     *
     * @param Collection $matches
     * @return array
     */
    public static function groupByWeek(Collection $matches): array
    {
        $grouped = [];

        foreach ($matches as $match) {
            $week = $match->week;
            if (!isset($grouped[$week])) {
                $grouped[$week] = [];
            }
            $grouped[$week][] = (new self($match))->toArray(request());
        }

        return $grouped;
    }
}
