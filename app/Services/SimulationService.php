<?php

namespace App\Services;

use App\DTOs\StandingDTO;
use App\Models\LeagueMatches;
use Illuminate\Support\Collection;

class SimulationService
{
    /**
     * Fetch all season data in a single query
     */
    private function getSeasonData(string $seasonId): Collection
    {
        return LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->orderBy('week')
            ->orderBy('id')
            ->get();
    }

    /**
     * Get complete simulation data with a single database query
     */
    public function getCompleteSimulationData(string $seasonId): array
    {
        $allMatches = $this->getSeasonData($seasonId);

        $standings = $this->calculateStandingsFromMatches($allMatches);
        $currentWeek = $this->getCurrentWeekFromMatches($allMatches);
        $predictions = $this->calculateChampionshipPredictions($standings, $currentWeek);
        $seasonComplete = $this->isSeasonCompleteFromMatches($allMatches);

        return [
            'standings' => $standings,
            'current_week' => $currentWeek,
            'predictions' => $predictions,
            'season_complete' => $seasonComplete,
            'matches' => $allMatches
        ];
    }

    /**
     * Calculate standings from pre-fetched matches
     */
    private function calculateStandingsFromMatches(Collection $allMatches): array
    {
        $teamStats = [];
        $playedMatches = $allMatches->where('is_played', true);

        // Initialize team stats from all matches
        foreach ($allMatches as $match) {
            if (!isset($teamStats[$match->homeTeam->name])) {
                $teamStats[$match->homeTeam->name] = new StandingDTO($match->homeTeam->name);
            }
            if (!isset($teamStats[$match->awayTeam->name])) {
                $teamStats[$match->awayTeam->name] = new StandingDTO($match->awayTeam->name);
            }
        }

        // Calculate stats from played matches
        foreach ($playedMatches as $match) {
            $homeTeam = $teamStats[$match->homeTeam->name];
            $awayTeam = $teamStats[$match->awayTeam->name];
            $homeScore = $match->home_score ?? 0;
            $awayScore = $match->away_score ?? 0;

            if ($homeScore > $awayScore) {
                $homeTeam->addWin();
                $awayTeam->addLoss();
            } elseif ($homeScore < $awayScore) {
                $awayTeam->addWin();
                $homeTeam->addLoss();
            } else {
                $homeTeam->addDraw();
                $awayTeam->addDraw();
            }
        }

        // Convert DTOs to arrays and sort by points (descending)
        $standings = array_map(fn(StandingDTO $dto) => $dto->toArray(), array_values($teamStats));
        usort($standings, function ($a, $b) {
            return $b['points'] - $a['points'];
        });

        return $standings;
    }

    /**
     * Legacy method - kept for backward compatibility
     */
    public function calculateStandings(string $seasonId): array
    {
        $allMatches = $this->getSeasonData($seasonId);
        return $this->calculateStandingsFromMatches($allMatches);
    }

    public function calculateChampionshipPredictions(array $standings, int $currentWeek): array
    {
        // Only calculate predictions if current week > 3
        if ($currentWeek <= 3) {
            return [];
        }

        $totalPoints = array_sum(array_column($standings, 'points'));
        $predictions = [];

        if ($totalPoints === 0) {
            $equalChance = round(100 / count($standings));
            foreach ($standings as $team) {
                $predictions[$team['team']] = $equalChance;
            }
        } else {
            foreach ($standings as $index => $team) {
                $baseChance = $totalPoints > 0 ? ($team['points'] / $totalPoints) * 100 : 25;
                $positionBonus = (count($standings) - $index) * 5;
                $predictions[$team['team']] = min(round($baseChance + $positionBonus), 100);
            }
        }

        // Sort predictions by percentage (descending)
        arsort($predictions);

        return $predictions;
    }

    /**
     * Get current week from pre-fetched matches
     */
    private function getCurrentWeekFromMatches(Collection $allMatches): int
    {
        $unplayedMatches = $allMatches->where('is_played', false);

        if ($unplayedMatches->isEmpty()) {
            // If no unplayed matches, return the maximum week (season is complete)
            $maxWeek = $allMatches->max('week');
            return $maxWeek ?? 1;
        }

        return $unplayedMatches->min('week');
    }

    /**
     * Check if season is complete from pre-fetched matches
     */
    private function isSeasonCompleteFromMatches(Collection $allMatches): bool
    {
        $maxWeek = $allMatches->max('week') ?? 0;

        if ($maxWeek < 6) {
            return false;
        }

        $lastWeekMatches = $allMatches->where('week', $maxWeek);
        return $lastWeekMatches->every(fn($match) => $match->is_played);
    }

    /**
     * Legacy method - kept for backward compatibility
     */
    public function getCurrentWeek(string $seasonId): int
    {
        $allMatches = $this->getSeasonData($seasonId);
        return $this->getCurrentWeekFromMatches($allMatches);
    }

    /**
     * Legacy method - kept for backward compatibility
     */
    public function isSeasonComplete(string $seasonId): bool
    {
        $allMatches = $this->getSeasonData($seasonId);
        return $this->isSeasonCompleteFromMatches($allMatches);
    }
}
