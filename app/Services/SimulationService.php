<?php

namespace App\Services;

use App\DTOs\StandingDTO;
use App\Models\LeagueMatches;

class SimulationService
{
    public function calculateStandings(string $seasonId): array
    {
        $matches = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->where('is_played', true)
            ->get();

        $teamStats = [];

        $allMatches = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->get();

        foreach ($allMatches as $match) {
            if (!isset($teamStats[$match->homeTeam->name])) {
                $teamStats[$match->homeTeam->name] = new StandingDTO($match->homeTeam->name);
            }
            if (!isset($teamStats[$match->awayTeam->name])) {
                $teamStats[$match->awayTeam->name] = new StandingDTO($match->awayTeam->name);
            }
        }

        foreach ($matches as $match) {
            $homeTeam = $teamStats[$match->homeTeam->name];
            $awayTeam = $teamStats[$match->awayTeam->name];
            $homeScore = $match->home_score ?? 0;
            $awayScore = $match->away_score ?? 0;

            if ($homeScore > $awayScore) {
                $homeTeam->addWin();
                $awayTeam->addLoss();
            } elseif ($homeScore < $awayScore) {
                $awayTeam->addWin();
                $homeTeam->addLoss();
            } else {
                $homeTeam->addDraw();
                $awayTeam->addDraw();
            }
        }

        // Convert DTOs to arrays and sort by points (descending)
        $standings = array_map(fn(StandingDTO $dto) => $dto->toArray(), array_values($teamStats));
        usort($standings, function ($a, $b) {
            return $b['points'] - $a['points'];
        });

        return $standings;
    }

    public function calculateChampionshipPredictions(array $standings, int $currentWeek): array
    {
        // Only calculate predictions if current week > 3
        if ($currentWeek <= 3) {
            return [];
        }

        $totalPoints = array_sum(array_column($standings, 'points'));
        $predictions = [];

        if ($totalPoints === 0) {
            $equalChance = round(100 / count($standings));
            foreach ($standings as $team) {
                $predictions[$team['team']] = $equalChance;
            }
        } else {
            foreach ($standings as $index => $team) {
                $baseChance = $totalPoints > 0 ? ($team['points'] / $totalPoints) * 100 : 25;
                $positionBonus = (count($standings) - $index) * 5;
                $predictions[$team['team']] = min(round($baseChance + $positionBonus), 100);
            }
        }

        // Sort predictions by percentage (descending)
        arsort($predictions);

        return $predictions;
    }

    public function getCurrentWeek(string $seasonId): int
    {
        $nextUnplayedWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        // If no unplayed matches, return the maximum week (season is complete)
        if (is_null($nextUnplayedWeek)) {
            $maxWeek = LeagueMatches::where('season_id', $seasonId)->max('week');
            return $maxWeek ?? 1;
        }

        return $nextUnplayedWeek;
    }

    public function isSeasonComplete(string $seasonId): bool
    {
        $maxWeek = LeagueMatches::where('season_id', $seasonId)->max('week') ?? 0;

        if ($maxWeek < 6) {
            return false;
        }

        $lastWeekMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $maxWeek)
            ->get();

        return $lastWeekMatches->every(fn($match) => $match->is_played);
    }
}
