interface Team {
    team: string;
    played: number;
    won: number;
    drawn: number;
    lost: number;
    points: number;
}

interface StandingsColumnProps {
    standings: Team[];
}

export default function StandingsColumn({ standings }: StandingsColumnProps) {
    return (
        <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-3 border-b border-gray-200">
                    <h2 className="text-lg font-medium text-gray-900">League Table</h2>
                </div>
                <div className="p-4">
                    <div className="overflow-x-auto">
                        <table className="min-w-full text-xs">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-2 font-medium text-gray-700">Pos</th>
                                    <th className="text-left py-2 font-medium text-gray-700">Team</th>
                                    <th className="text-center py-2 font-medium text-gray-700">P</th>
                                    <th className="text-center py-2 font-medium text-gray-700">W</th>
                                    <th className="text-center py-2 font-medium text-gray-700">D</th>
                                    <th className="text-center py-2 font-medium text-gray-700">L</th>
                                    <th className="text-center py-2 font-medium text-gray-700">Pts</th>
                                </tr>
                            </thead>
                            <tbody>
                                {standings.map((team, index) => (
                                    <tr key={team.team} className="border-b border-gray-100">
                                        <td className="py-2 text-gray-900 font-medium">{index + 1}</td>
                                        <td className="py-2 text-gray-900">{team.team}</td>
                                        <td className="py-2 text-center text-gray-600">{team.played}</td>
                                        <td className="py-2 text-center text-gray-600">{team.won}</td>
                                        <td className="py-2 text-center text-gray-600">{team.drawn}</td>
                                        <td className="py-2 text-center text-gray-600">{team.lost}</td>
                                        <td className="py-2 text-center text-gray-900 font-bold">{team.points}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}
