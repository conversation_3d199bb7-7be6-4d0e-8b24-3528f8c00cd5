export interface Team {
    id: number;
    name: string;
}

export interface Match {
    home_team: Team;
    away_team: Team;
    week: number;
    home_score?: number;
    away_score?: number;
    is_played?: boolean;
}

export interface Standing {
    team: string;
    played: number;
    won: number;
    drawn: number;
    lost: number;
    points: number;
}

export interface SimulationProps {
    fixtures: Record<number, Match[]>; // Now grouped by week
    standings: Standing[];
    predictions: ChampionshipPredictions;
    current_week: number;
    season_complete: boolean;
    seasonId: string;
}

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface PlayWeekResponse {
    fixtures?: Match[];
    standings?: Standing[];
    predictions?: ChampionshipPredictions;
    current_week?: number;
    season_complete?: boolean;
    played_count?: number;
    week?: number;
    message?: string;
}

export interface FixturesByWeek {
    [week: number]: Match[];
}

export interface ChampionshipPredictions {
    [teamName: string]: number;
}
