import { Head, router } from '@inertiajs/react';
import { useMemo, useState } from 'react';
import { RotateCcw } from 'lucide-react';
import { SimulationProps } from '@/types/simulation';
import StandingsColumn from '@/components/league-simulation/StandingsColumn';
import FixturesColumn from '@/components/league-simulation/FixturesColumn';
import PredictionsColumn from '@/components/league-simulation/PredictionsColumn';

export default function Simulation({
    fixtures,
    standings,
    predictions,
    current_week,
    season_complete,
    seasonId
}: SimulationProps) {
    const [currentFixtures, setCurrentFixtures] = useState(fixtures);
    const [currentStandings, setCurrentStandings] = useState(standings);
    const [currentPredictions, setCurrentPredictions] = useState(predictions);
    const [buttonsDisabled, setButtonsDisabled] = useState(season_complete);
    const [currentSeasonId] = useState(seasonId);

    const fixturesByWeek = currentFixtures;
    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));

    const handleFixturesUpdate = (data: any) => {
        if (data.fixtures) setCurrentFixtures(data.fixtures);
        if (data.standings) setCurrentStandings(data.standings);
        if (data.predictions) setCurrentPredictions(data.predictions);
        if (data.season_complete !== undefined) setButtonsDisabled(data.season_complete);
    };

    const handleReset = () => {
        router.visit('/', { method: 'get' });
    };

    return (
        <>
            <Head title="League Simulation" />
            <div className="min-h-screen bg-gray-100 py-8">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold text-gray-900">League Simulation</h1>
                    </div>

                    <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                        <StandingsColumn standings={currentStandings} />

                        <FixturesColumn
                            fixturesByWeek={fixturesByWeek}
                            weekNumbers={weekNumbers}
                            buttonsDisabled={buttonsDisabled}
                            seasonId={currentSeasonId.toString()}
                            onFixturesUpdate={handleFixturesUpdate}
                        />

                        <PredictionsColumn predictions={currentPredictions} />
                    </div>

                    {/* Reset Button - Bottom Right */}
                    <div className="fixed right-6 bottom-6">
                        <button
                            onClick={handleReset}
                            className="flex items-center space-x-2 rounded-lg bg-red-600 px-4 py-2 text-white shadow-lg transition-colors hover:bg-red-700"
                        >
                            <RotateCcw size={16} />
                            <span>Reset</span>
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}
